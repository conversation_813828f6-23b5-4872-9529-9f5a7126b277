{"timestamp": "2025-07-29T22:28:31.714662", "level": "INFO", "logger": "setup_database", "message": "开始初始化数据库...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.715662", "level": "INFO", "logger": "setup_database", "message": "数据库表创建成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.726175", "level": "INFO", "logger": "setup_database", "message": "用户表记录数: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.726175", "level": "INFO", "logger": "setup_database", "message": "日志表记录数: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.735964", "level": "INFO", "logger": "setup_database", "message": "数据库统计信息: {'total_records': 0, 'unique_users': 0, 'total_comments': 0, 'unique_posts': 0, 'verified_users': 0, 'latest_extraction': None, 'database_size_mb': 0.05078125}", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.736962", "level": "INFO", "logger": "setup_database", "message": "数据库初始化完成", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.737956", "level": "INFO", "logger": "setup_database", "message": "创建示例数据...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.750460", "level": "INFO", "logger": "setup_database", "message": "成功创建 3 条示例数据", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.751969", "level": "INFO", "logger": "setup_database", "message": "数据库验证成功 - 用户: 3, 日志: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:28:31.754975", "level": "INFO", "logger": "setup_database", "message": "认证用户: 1, 私人账户: 1", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:31:30.426867", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:31:30.427877", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:31:30.447721", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:31:30.448723", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:33:52.237598", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:33:52.238858", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:33:52.277044", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:33:52.277044", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:37:18.490742", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:37:18.491748", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:37:18.513970", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:37:18.513970", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:37:39.559054", "level": "INFO", "logger": "web_app", "message": "Session ID验证请求", "module": "logger", "function": "_log", "line": 145, "extra": {"session_id_length": "***"}}
{"timestamp": "2025-07-29T22:38:01.905007", "level": "INFO", "logger": "web_app", "message": "开始数据提取", "module": "logger", "function": "_log", "line": 145, "extra": {"valid_uris_count": 1, "invalid_uris_count": 0}}
{"timestamp": "2025-07-29T22:49:06.965570", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T22:49:06.981798", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:49:06.981798", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:49:07.000815", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:49:07.001815", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:53:39.491717", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T22:53:39.500364", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:53:39.501868", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:53:39.521868", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:53:39.521868", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.804001", "level": "INFO", "logger": "setup_database", "message": "开始初始化数据库...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.805001", "level": "INFO", "logger": "setup_database", "message": "数据库表创建成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.817352", "level": "INFO", "logger": "setup_database", "message": "用户表记录数: 3", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.817352", "level": "INFO", "logger": "setup_database", "message": "日志表记录数: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.826339", "level": "INFO", "logger": "setup_database", "message": "数据库统计信息: {'total_records': 3, 'unique_users': 3, 'total_comments': 3, 'unique_posts': 1, 'verified_users': 1, 'latest_extraction': '2025-07-29T22:28:31.737956', 'database_size_mb': 0.05078125}", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.826339", "level": "INFO", "logger": "setup_database", "message": "数据库初始化完成", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.827340", "level": "INFO", "logger": "setup_database", "message": "数据库中已有 3 条记录，跳过示例数据创建", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.829550", "level": "INFO", "logger": "setup_database", "message": "数据库验证成功 - 用户: 3, 日志: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:22.834098", "level": "INFO", "logger": "setup_database", "message": "认证用户: 1, 私人账户: 1", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T22:58:56.450887", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:00:02.418280", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986", "user_id": "75995718761"}}
{"timestamp": "2025-07-29T23:00:02.418787", "level": "INFO", "logger": "web_app", "message": "Session ID验证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986"}}
{"timestamp": "2025-07-29T23:00:24.717208", "level": "INFO", "logger": "web_app", "message": "开始数据提取", "module": "logger", "function": "_log", "line": 145, "extra": {"valid_uris_count": 1, "invalid_uris_count": 0}}
{"timestamp": "2025-07-29T23:00:24.719222", "level": "INFO", "logger": "task_manager", "message": "创建新任务", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "19af37db-f92f-49fa-8921-89cdb176de00", "uri_count": 1}}
{"timestamp": "2025-07-29T23:00:24.719222", "level": "INFO", "logger": "task_19af37db-f92f-49fa-8921-89cdb176de00", "message": "任务开始", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "19af37db-f92f-49fa-8921-89cdb176de00", "total_uris": 1}}
{"timestamp": "2025-07-29T23:00:24.719222", "level": "INFO", "logger": "task_manager", "message": "任务启动", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "19af37db-f92f-49fa-8921-89cdb176de00"}}
{"timestamp": "2025-07-29T23:00:24.720468", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:01:28.326586", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986", "user_id": "75995718761"}}
{"timestamp": "2025-07-29T23:01:28.326586", "level": "INFO", "logger": "instagram_client", "message": "开始提取媒体评论", "module": "logger", "function": "_log", "line": 145, "extra": {"media_url": "https://www.instagram.com/p/DL7cUUOyyR6/"}}
{"timestamp": "2025-07-29T23:01:28.326586", "level": "INFO", "logger": "instagram_client", "message": "获取媒体ID成功", "module": "logger", "function": "_log", "line": 145, "extra": {"media_pk": "3673654462465647738"}}
{"timestamp": "2025-07-29T23:02:33.876837", "level": "INFO", "logger": "instagram_client", "message": "获取评论成功", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_count": 8}}
{"timestamp": "2025-07-29T23:03:25.560711", "level": "INFO", "logger": "task_19af37db-f92f-49fa-8921-89cdb176de00", "message": "任务停止请求", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "19af37db-f92f-49fa-8921-89cdb176de00"}}
{"timestamp": "2025-07-29T23:03:25.560711", "level": "INFO", "logger": "web_app", "message": "停止数据提取", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": null}}
{"timestamp": "2025-07-29T23:12:20.468761", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:12:20.478285", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:12:20.480283", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:12:20.498340", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:12:20.498340", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:24:26.148379", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:24:26.157886", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:24:26.158889", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:24:26.179745", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:24:26.179745", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:26:37.375321", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:27:43.778709", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986", "user_id": "75995718761"}}
{"timestamp": "2025-07-29T23:27:43.778709", "level": "INFO", "logger": "web_app", "message": "Session ID验证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986"}}
{"timestamp": "2025-07-29T23:27:47.606036", "level": "INFO", "logger": "web_app", "message": "开始数据提取", "module": "logger", "function": "_log", "line": 145, "extra": {"valid_uris_count": 1, "invalid_uris_count": 0}}
{"timestamp": "2025-07-29T23:27:47.607028", "level": "INFO", "logger": "task_manager", "message": "创建新任务", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e94de37f-69f7-4413-b02c-182b2dc7b832", "uri_count": 1}}
{"timestamp": "2025-07-29T23:27:47.608274", "level": "INFO", "logger": "task_e94de37f-69f7-4413-b02c-182b2dc7b832", "message": "任务开始", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e94de37f-69f7-4413-b02c-182b2dc7b832", "total_uris": 1}}
{"timestamp": "2025-07-29T23:27:47.608274", "level": "INFO", "logger": "task_manager", "message": "任务启动", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e94de37f-69f7-4413-b02c-182b2dc7b832"}}
{"timestamp": "2025-07-29T23:27:47.609281", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:28:51.860348", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986", "user_id": "75995718761"}}
{"timestamp": "2025-07-29T23:28:51.860348", "level": "INFO", "logger": "task_e94de37f-69f7-4413-b02c-182b2dc7b832", "message": "开始提取URI: https://www.instagram.com/p/DL7cUUOyyR6/", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:28:51.860348", "level": "INFO", "logger": "instagram_client", "message": "开始提取媒体评论", "module": "logger", "function": "_log", "line": 145, "extra": {"media_url": "https://www.instagram.com/p/DL7cUUOyyR6/"}}
{"timestamp": "2025-07-29T23:28:51.860348", "level": "INFO", "logger": "instagram_client", "message": "获取媒体ID成功", "module": "logger", "function": "_log", "line": 145, "extra": {"media_pk": "3673654462465647738"}}
{"timestamp": "2025-07-29T23:29:57.026373", "level": "INFO", "logger": "instagram_client", "message": "获取评论成功", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_count": 8}}
{"timestamp": "2025-07-29T23:29:57.026373", "level": "INFO", "logger": "instagram_client", "message": "处理评论 1/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "ghstsprrw"}}
{"timestamp": "2025-07-29T23:29:57.245096", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 0, "user_id": "64450863624", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:57.245096", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "64450863624"}}
{"timestamp": "2025-07-29T23:29:57.245096", "level": "INFO", "logger": "instagram_client", "message": "处理评论 2/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "little_stephy0925"}}
{"timestamp": "2025-07-29T23:29:57.631928", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 1, "user_id": "201973571", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:57.632610", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "201973571"}}
{"timestamp": "2025-07-29T23:29:57.632610", "level": "INFO", "logger": "instagram_client", "message": "处理评论 3/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "hayson.capricorn"}}
{"timestamp": "2025-07-29T23:29:57.967799", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 2, "user_id": "1167773802", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:57.967799", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "1167773802"}}
{"timestamp": "2025-07-29T23:29:57.967799", "level": "INFO", "logger": "instagram_client", "message": "处理评论 4/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "parker_film"}}
{"timestamp": "2025-07-29T23:29:58.361537", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 3, "user_id": "67183139940", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:58.361537", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "67183139940"}}
{"timestamp": "2025-07-29T23:29:58.362299", "level": "INFO", "logger": "instagram_client", "message": "处理评论 5/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "spoonek9"}}
{"timestamp": "2025-07-29T23:29:58.861992", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 4, "user_id": "31854822", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:58.862497", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "31854822"}}
{"timestamp": "2025-07-29T23:29:58.862497", "level": "INFO", "logger": "instagram_client", "message": "处理评论 6/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "blurry_gaze"}}
{"timestamp": "2025-07-29T23:29:59.327487", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 5, "user_id": "74690646232", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:59.328492", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "74690646232"}}
{"timestamp": "2025-07-29T23:29:59.328492", "level": "INFO", "logger": "instagram_client", "message": "处理评论 7/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "lorisuals"}}
{"timestamp": "2025-07-29T23:29:59.623961", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 6, "user_id": "6125207582", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:59.623961", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "6125207582"}}
{"timestamp": "2025-07-29T23:29:59.623961", "level": "INFO", "logger": "instagram_client", "message": "处理评论 8/8", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "vin.coemgenus"}}
{"timestamp": "2025-07-29T23:29:59.852298", "level": "ERROR", "logger": "instagram_client", "message": "处理评论失败", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_index": 7, "user_id": "8296517837", "error": "'UserShort' object has no attribute 'is_verified'"}}
{"timestamp": "2025-07-29T23:29:59.852298", "level": "INFO", "logger": "instagram_client", "message": "保存最基本信息成功", "module": "logger", "function": "_log", "line": 145, "extra": {"user_id": "8296517837"}}
{"timestamp": "2025-07-29T23:29:59.852298", "level": "INFO", "logger": "instagram_client", "message": "媒体评论提取完成", "module": "logger", "function": "_log", "line": 145, "extra": {"total_comments": 8, "extracted_users": 8}}
{"timestamp": "2025-07-29T23:29:59.879251", "level": "INFO", "logger": "task_e94de37f-69f7-4413-b02c-182b2dc7b832", "message": "批量保存用户数据成功", "module": "logger", "function": "_log", "line": 145, "extra": {"total_users": 8, "saved_count": 8, "skipped_count": 0}}
{"timestamp": "2025-07-29T23:29:59.879251", "level": "INFO", "logger": "task_e94de37f-69f7-4413-b02c-182b2dc7b832", "message": "数据保存成功", "module": "logger", "function": "_log", "line": 145, "extra": {"extracted_count": 8, "saved_count": 8}}
{"timestamp": "2025-07-29T23:29:59.879251", "level": "INFO", "logger": "task_e94de37f-69f7-4413-b02c-182b2dc7b832", "message": "URI处理成功", "module": "logger", "function": "_log", "line": 145, "extra": {"uri": "https://www.instagram.com/p/DL7cUUOyyR6/", "user_count": 8}}
{"timestamp": "2025-07-29T23:29:59.879251", "level": "INFO", "logger": "task_e94de37f-69f7-4413-b02c-182b2dc7b832", "message": "任务结束", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e94de37f-69f7-4413-b02c-182b2dc7b832", "status": "completed", "total_users": 8, "successful": 1, "failed": 0}}
{"timestamp": "2025-07-29T23:35:04.637640", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "csv", "limit": 1000}}
{"timestamp": "2025-07-29T23:35:11.051025", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "excel", "limit": 1000}}
{"timestamp": "2025-07-29T23:37:23.046834", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:37:23.054895", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:37:23.055897", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:37:23.075899", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:37:23.075899", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:38:09.487029", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:38:09.497047", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:38:09.498041", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:38:09.524945", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:38:09.526143", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:38:56.050630", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "csv", "limit": 1000}}
{"timestamp": "2025-07-29T23:38:56.054145", "level": "INFO", "logger": "web_app", "message": "导出文件创建成功", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233856.csv", "record_count": 11}}
{"timestamp": "2025-07-29T23:38:56.060606", "level": "INFO", "logger": "web_app", "message": "开始下载文件", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233856.csv"}}
{"timestamp": "2025-07-29T23:38:56.062614", "level": "ERROR", "logger": "web_app", "message": "下载文件失败: [WinError 3] 系统找不到指定的路径。: 'E:\\\\Desktop\\\\is\\\\is\\\\src\\\\ui\\\\data\\\\exports\\\\instagram_data_20250729_233856.csv'", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233856.csv"}}
{"timestamp": "2025-07-29T23:39:02.034248", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "csv", "limit": 1000}}
{"timestamp": "2025-07-29T23:39:02.036670", "level": "INFO", "logger": "web_app", "message": "导出文件创建成功", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233902.csv", "record_count": 11}}
{"timestamp": "2025-07-29T23:39:02.043834", "level": "INFO", "logger": "web_app", "message": "开始下载文件", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233902.csv"}}
{"timestamp": "2025-07-29T23:39:02.044840", "level": "ERROR", "logger": "web_app", "message": "下载文件失败: [WinError 3] 系统找不到指定的路径。: 'E:\\\\Desktop\\\\is\\\\is\\\\src\\\\ui\\\\data\\\\exports\\\\instagram_data_20250729_233902.csv'", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233902.csv"}}
{"timestamp": "2025-07-29T23:39:04.706279", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "excel", "limit": 1000}}
{"timestamp": "2025-07-29T23:39:04.707782", "level": "INFO", "logger": "web_app", "message": "导出文件创建成功", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233904.excel", "record_count": 11}}
{"timestamp": "2025-07-29T23:39:04.712442", "level": "ERROR", "logger": "web_app", "message": "下载文件不存在", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233904.excel"}}
{"timestamp": "2025-07-29T23:39:06.491496", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "json", "limit": 1000}}
{"timestamp": "2025-07-29T23:39:06.493636", "level": "INFO", "logger": "web_app", "message": "导出文件创建成功", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233906.json", "record_count": 11}}
{"timestamp": "2025-07-29T23:39:06.499175", "level": "INFO", "logger": "web_app", "message": "开始下载文件", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233906.json"}}
{"timestamp": "2025-07-29T23:39:06.500176", "level": "ERROR", "logger": "web_app", "message": "下载文件失败: [WinError 3] 系统找不到指定的路径。: 'E:\\\\Desktop\\\\is\\\\is\\\\src\\\\ui\\\\data\\\\exports\\\\instagram_data_20250729_233906.json'", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233906.json"}}
{"timestamp": "2025-07-29T23:39:29.448047", "level": "INFO", "logger": "web_app", "message": "导出数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"format": "csv", "limit": 1000}}
{"timestamp": "2025-07-29T23:39:29.451621", "level": "INFO", "logger": "web_app", "message": "导出文件创建成功", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233929.csv", "record_count": 11}}
{"timestamp": "2025-07-29T23:39:29.457883", "level": "INFO", "logger": "web_app", "message": "开始下载文件", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233929.csv"}}
{"timestamp": "2025-07-29T23:39:29.457883", "level": "ERROR", "logger": "web_app", "message": "下载文件失败: [WinError 3] 系统找不到指定的路径。: 'E:\\\\Desktop\\\\is\\\\is\\\\src\\\\ui\\\\data\\\\exports\\\\instagram_data_20250729_233929.csv'", "module": "logger", "function": "_log", "line": 145, "extra": {"filename": "instagram_data_20250729_233929.csv"}}
{"timestamp": "2025-07-29T23:45:01.038230", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:45:01.053407", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:01.055408", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:01.075492", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:01.075492", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:22.136254", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:45:22.143915", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:22.144915", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:22.164283", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:22.164283", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:53.171874", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:45:53.180173", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:53.181173", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:53.201232", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:45:53.202746", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:46:51.369278", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:46:51.379457", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:46:51.380457", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:46:51.401503", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:46:51.401503", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:49:32.873672", "level": "INFO", "logger": "web_app", "message": "删除用户数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"user_count": 3}}
{"timestamp": "2025-07-29T23:49:32.887204", "level": "INFO", "logger": "web_app", "message": "用户数据删除成功", "module": "logger", "function": "_log", "line": 145, "extra": {"deleted_count": 3, "usernames": ["sample_user_1", "sample_user_2", "sample_user_3"]}}
{"timestamp": "2025-07-29T23:53:41.254743", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:53:41.254743", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:54:45.076549", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "beliwe74986", "user_id": "75995718761"}}
{"timestamp": "2025-07-29T23:56:54.358430", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:56:54.368321", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:56:54.369298", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:56:54.390089", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:56:54.390656", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:57:49.323579", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-29T23:57:49.331678", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:57:49.332684", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:57:49.356891", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:57:49.356891", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-29T23:59:11.623973", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T00:00:17.360055", "level": "ERROR", "logger": "instagram_client", "message": "Session ID无效或已过期", "module": "logger", "function": "_log", "line": 145, "extra": {"error": "login_required"}}
{"timestamp": "2025-07-30T00:00:17.360055", "level": "ERROR", "logger": "web_app", "message": "Instagram认证失败: Session ID无效或已过期", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T00:02:33.820604", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T00:03:40.164977", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "jins2584", "user_id": "76368665610"}}
{"timestamp": "2025-07-30T00:03:40.173624", "level": "INFO", "logger": "web_app", "message": "Session ID验证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "jins2584"}}
{"timestamp": "2025-07-30T00:03:46.139234", "level": "INFO", "logger": "web_app", "message": "开始数据提取", "module": "logger", "function": "_log", "line": 145, "extra": {"valid_uris_count": 1, "invalid_uris_count": 0}}
{"timestamp": "2025-07-30T00:03:46.139234", "level": "INFO", "logger": "task_manager", "message": "创建新任务", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "796244e1-30b9-4148-86c1-2f54d33b5646", "uri_count": 1}}
{"timestamp": "2025-07-30T00:03:46.140321", "level": "INFO", "logger": "task_796244e1-30b9-4148-86c1-2f54d33b5646", "message": "任务开始", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "796244e1-30b9-4148-86c1-2f54d33b5646", "total_uris": 1}}
{"timestamp": "2025-07-30T00:03:46.140321", "level": "INFO", "logger": "task_manager", "message": "任务启动", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "796244e1-30b9-4148-86c1-2f54d33b5646"}}
{"timestamp": "2025-07-30T00:03:46.140321", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T00:04:52.011078", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "jins2584", "user_id": "76368665610"}}
{"timestamp": "2025-07-30T00:04:52.011591", "level": "INFO", "logger": "task_796244e1-30b9-4148-86c1-2f54d33b5646", "message": "开始提取URI: https://www.instagram.com/p/DMebBhrRiZB/", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T00:04:52.011591", "level": "INFO", "logger": "instagram_client", "message": "开始提取媒体评论", "module": "logger", "function": "_log", "line": 145, "extra": {"media_url": "https://www.instagram.com/p/DMebBhrRiZB/"}}
{"timestamp": "2025-07-30T00:04:52.011591", "level": "INFO", "logger": "instagram_client", "message": "获取媒体ID成功", "module": "logger", "function": "_log", "line": 145, "extra": {"media_pk": "3683500397370418753"}}
{"timestamp": "2025-07-30T00:05:57.008147", "level": "INFO", "logger": "instagram_client", "message": "获取评论成功", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_count": 2}}
{"timestamp": "2025-07-30T00:05:57.008656", "level": "INFO", "logger": "instagram_client", "message": "处理评论 1/2", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "limonatamber"}}
{"timestamp": "2025-07-30T00:05:57.503738", "level": "INFO", "logger": "instagram_client", "message": "用户信息保存完成", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "limonatamber", "user_id": "239882951", "is_private": true, "comment_preview": "明星❤️❤️❤️❤️"}}
{"timestamp": "2025-07-30T00:05:57.503738", "level": "INFO", "logger": "instagram_client", "message": "处理评论 2/2", "module": "logger", "function": "_log", "line": 145, "extra": {"comment_user": "bubblelane"}}
{"timestamp": "2025-07-30T00:05:58.003731", "level": "INFO", "logger": "instagram_client", "message": "用户信息保存完成", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "bubblelane", "user_id": "353779250", "is_private": true, "comment_preview": "補個滋養～50多年前就是城內人熟知的西點麵包店了"}}
{"timestamp": "2025-07-30T00:05:58.004244", "level": "INFO", "logger": "instagram_client", "message": "媒体评论提取完成", "module": "logger", "function": "_log", "line": 145, "extra": {"total_comments": 2, "extracted_users": 2}}
{"timestamp": "2025-07-30T00:05:58.026144", "level": "INFO", "logger": "task_796244e1-30b9-4148-86c1-2f54d33b5646", "message": "批量保存用户数据成功", "module": "logger", "function": "_log", "line": 145, "extra": {"total_users": 2, "saved_count": 2, "skipped_count": 0}}
{"timestamp": "2025-07-30T00:05:58.026144", "level": "INFO", "logger": "task_796244e1-30b9-4148-86c1-2f54d33b5646", "message": "数据保存成功", "module": "logger", "function": "_log", "line": 145, "extra": {"extracted_count": 2, "saved_count": 2}}
{"timestamp": "2025-07-30T00:05:58.027145", "level": "INFO", "logger": "task_796244e1-30b9-4148-86c1-2f54d33b5646", "message": "URI处理成功", "module": "logger", "function": "_log", "line": 145, "extra": {"uri": "https://www.instagram.com/p/DMebBhrRiZB/", "user_count": 2}}
{"timestamp": "2025-07-30T00:05:58.027145", "level": "INFO", "logger": "task_796244e1-30b9-4148-86c1-2f54d33b5646", "message": "任务结束", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "796244e1-30b9-4148-86c1-2f54d33b5646", "status": "completed", "total_users": 2, "successful": 1, "failed": 0}}
{"timestamp": "2025-07-30T22:21:05.169206", "level": "INFO", "logger": "setup_database", "message": "开始初始化数据库...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.171801", "level": "INFO", "logger": "setup_database", "message": "数据库表创建成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.179587", "level": "INFO", "logger": "setup_database", "message": "用户表记录数: 10", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.180695", "level": "INFO", "logger": "setup_database", "message": "日志表记录数: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.195635", "level": "INFO", "logger": "setup_database", "message": "数据库统计信息: {'total_records': 10, 'unique_users': 10, 'total_comments': 10, 'unique_posts': 2, 'verified_users': 0, 'latest_extraction': '2025-07-30T00:05:58.003730', 'database_size_mb': 0.05078125}", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.196648", "level": "INFO", "logger": "setup_database", "message": "数据库初始化完成", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.198650", "level": "INFO", "logger": "setup_database", "message": "数据库中已有 10 条记录，跳过示例数据创建", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.203754", "level": "INFO", "logger": "setup_database", "message": "数据库验证成功 - 用户: 10, 日志: 0", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:05.211854", "level": "INFO", "logger": "setup_database", "message": "认证用户: 0, 私人账户: 2", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:25.801761", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:21:25.810779", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:25.812560", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:25.829301", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:21:25.829301", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:22:56.889839", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:22:56.895498", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:22:56.895498", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:22:56.912114", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5000", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:22:56.912114", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:23:57.148748", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:24:08.103106", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:24:11.316379", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:24:55.418034", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:25:08.041816", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:25:08.047048", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:25:08.047976", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:25:08.072810", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5001", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:25:08.072810", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:26:08.413318", "level": "INFO", "logger": "instagram_client", "message": "Instagram客户端配置完成", "module": "logger", "function": "_log", "line": 145, "extra": {"delay_range": [1, 3], "timeout": 30}}
{"timestamp": "2025-07-30T22:26:08.419360", "level": "INFO", "logger": "main", "message": "应用启动中...", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:26:08.421017", "level": "INFO", "logger": "main", "message": "数据库初始化成功", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:26:08.437736", "level": "INFO", "logger": "main", "message": "启动Web服务器: http://127.0.0.1:5002", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:26:08.439344", "level": "INFO", "logger": "main", "message": "调试模式: 开启", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:30:40.020386", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:31:43.975990", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "tiyoxeb733", "user_id": "76225328255"}}
{"timestamp": "2025-07-30T22:31:43.975990", "level": "INFO", "logger": "web_app", "message": "Session ID验证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "tiyoxeb733"}}
{"timestamp": "2025-07-30T22:31:50.350133", "level": "INFO", "logger": "web_app", "message": "开始数据提取", "module": "logger", "function": "_log", "line": 145, "extra": {"valid_uris_count": 23, "invalid_uris_count": 0}}
{"timestamp": "2025-07-30T22:31:50.352205", "level": "INFO", "logger": "task_manager", "message": "创建新任务", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e623d3de-fedc-4a2d-809f-331590d39504", "uri_count": 23}}
{"timestamp": "2025-07-30T22:31:50.352640", "level": "INFO", "logger": "task_e623d3de-fedc-4a2d-809f-331590d39504", "message": "任务开始", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e623d3de-fedc-4a2d-809f-331590d39504", "total_uris": 23}}
{"timestamp": "2025-07-30T22:31:50.352640", "level": "INFO", "logger": "task_manager", "message": "任务启动", "module": "logger", "function": "_log", "line": 145, "extra": {"task_id": "e623d3de-fedc-4a2d-809f-331590d39504"}}
{"timestamp": "2025-07-30T22:31:50.352640", "level": "INFO", "logger": "instagram_client", "message": "开始Session ID认证", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:32:17.992086", "level": "INFO", "logger": "web_app", "message": "删除用户数据请求", "module": "logger", "function": "_log", "line": 145, "extra": {"user_count": 10}}
{"timestamp": "2025-07-30T22:32:18.011685", "level": "INFO", "logger": "web_app", "message": "用户数据删除成功", "module": "logger", "function": "_log", "line": 145, "extra": {"deleted_count": 10, "usernames": ["ghstsprrw", "little_stephy0925", "hayson.capricorn", "parker_film", "spoonek9"]}}
{"timestamp": "2025-07-30T22:32:53.577521", "level": "INFO", "logger": "instagram_client", "message": "Session ID认证成功", "module": "logger", "function": "_log", "line": 145, "extra": {"username": "tiyoxeb733", "user_id": "76225328255"}}
{"timestamp": "2025-07-30T22:32:53.578109", "level": "INFO", "logger": "task_e623d3de-fedc-4a2d-809f-331590d39504", "message": "开始提取URI: https://www.instagram.com/p/DFsD57-ocAC/", "module": "logger", "function": "_log", "line": 145}
{"timestamp": "2025-07-30T22:32:53.578109", "level": "INFO", "logger": "instagram_client", "message": "开始提取媒体评论", "module": "logger", "function": "_log", "line": 145, "extra": {"media_url": "https://www.instagram.com/p/DFsD57-ocAC/"}}
{"timestamp": "2025-07-30T22:32:53.578109", "level": "INFO", "logger": "instagram_client", "message": "获取媒体ID成功", "module": "logger", "function": "_log", "line": 145, "extra": {"media_pk": "3561238580894482434"}}
